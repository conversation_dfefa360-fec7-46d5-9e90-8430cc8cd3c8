"""
聊天消息和推荐相关数据模型
"""

from sqlalchemy import Column, String, Boolean, DateTime, Text, Integer, ForeignKey, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid
from datetime import datetime
from typing import Dict, Any, Optional

from app.database import Base


class ChatMessage(Base):
    """聊天消息模型"""

    __tablename__ = "chat_messages"

    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    uuid = Column(UUID(as_uuid=True), unique=True, default=uuid.uuid4, index=True, nullable=False, comment="消息UUID")

    # 关联信息
    conversation_id = Column(UUID(as_uuid=True), ForeignKey('ai_conversations.uuid'), nullable=False, index=True, comment="对话ID")
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False, index=True, comment="用户ID")

    # 消息信息
    message_type = Column(String(20), nullable=False, comment="消息类型：user/assistant")
    content = Column(Text, nullable=False, comment="消息内容")
    message_id = Column(String(100), comment="AI返回的消息ID")
    
    # 状态信息
    is_complete = Column(Boolean, default=False, nullable=False, comment="消息是否完整")
    is_deleted = Column(Boolean, default=False, nullable=False, comment="是否删除")

    # 时间信息
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")
    deleted_at = Column(DateTime(timezone=True), comment="删除时间")

    # 关系
    user = relationship("User", backref="chat_messages")

    def __repr__(self):
        return f"<ChatMessage(id={self.id}, uuid={self.uuid}, type={self.message_type}, conversation_id={self.conversation_id})>"

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "message_id": str(self.uuid),
            "conversation_id": self.conversation_id,
            "user_id": self.user_id,
            "message_type": self.message_type,
            "content": self.content,
            "is_complete": self.is_complete,
            "is_deleted": self.is_deleted,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

    def soft_delete(self):
        """软删除"""
        from app.utils.timezone import to_utc_for_db
        self.is_deleted = True
        self.deleted_at = to_utc_for_db()


class CarRecommendation(Base):
    """车辆推荐模型"""

    __tablename__ = "car_recommendations"

    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    uuid = Column(UUID(as_uuid=True), unique=True, default=uuid.uuid4, index=True, nullable=False, comment="推荐UUID")

    # 关联信息
    conversation_id = Column(UUID(as_uuid=True), ForeignKey('ai_conversations.uuid'), nullable=False, index=True, comment="对话ID")
    message_id = Column(String(100), nullable=False, index=True, comment="关联的消息ID")
    chat_message_uuid = Column(UUID(as_uuid=True), ForeignKey('chat_messages.uuid'), nullable=True, index=True, comment="关联的聊天消息UUID")

    # 推荐信息
    car_id = Column(String(50), nullable=False, comment="真实车辆ID（不对外暴露）")
    car_uuid = Column(UUID(as_uuid=True), nullable=False, index=True, comment="对外暴露的车辆UUID")
    task_type = Column(String(50), nullable=False, comment="任务类型")
    recommendation_data = Column(JSON, comment="完整推荐数据")
    slots_data = Column(JSON, comment="槽位数据")

    # 状态信息
    is_deleted = Column(Boolean, default=False, nullable=False, comment="是否删除")

    # 时间信息
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")
    deleted_at = Column(DateTime(timezone=True), comment="删除时间")

    # 关系
    chat_message = relationship("ChatMessage", backref="car_recommendations")

    def __repr__(self):
        return f"<CarRecommendation(id={self.id}, uuid={self.uuid}, car_id={self.car_id}, task_type={self.task_type})>"

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典（隐藏真实car_id）"""
        return {
            "id": str(self.uuid),  # 使用UUID作为对外ID
            "conversation_id": self.conversation_id,
            "message_id": self.message_id,
            "car_id": str(self.car_uuid),  # 使用car_uuid作为对外车辆ID
            "task_type": self.task_type,
            "recommendation_data": self.recommendation_data,
            "slots_data": self.slots_data,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

    def to_dict_with_car_id(self) -> Dict[str, Any]:
        """转换为字典（包含真实car_id，仅内部使用）"""
        result = self.to_dict()
        result["real_car_id"] = self.car_id  # 真实车辆ID
        result["car_uuid"] = str(self.car_uuid)  # 对外车辆UUID
        return result

    def soft_delete(self):
        """软删除"""
        from app.utils.timezone import to_utc_for_db
        self.is_deleted = True
        self.deleted_at = to_utc_for_db()


class CarIdMapping(Base):
    """车辆ID映射模型 - 车辆真实ID与UUID的对照表"""

    __tablename__ = "car_id_mappings"

    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    uuid = Column(UUID(as_uuid=True), unique=True, default=uuid.uuid4, index=True, nullable=False, comment="映射记录UUID")

    # 映射信息
    real_car_id = Column(String(50), unique=True, nullable=False, index=True, comment="真实车辆ID")
    car_uuid = Column(UUID(as_uuid=True), unique=True, nullable=False, index=True, comment="对外暴露的车辆UUID")

    # 统计信息
    first_seen_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False, comment="首次出现时间")
    last_used_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False, comment="最后使用时间")
    usage_count = Column(Integer, default=1, nullable=False, comment="使用次数")

    # 状态信息
    is_deleted = Column(Boolean, default=False, nullable=False, comment="是否删除")

    # 时间信息
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")
    deleted_at = Column(DateTime(timezone=True), comment="删除时间")

    def __repr__(self):
        return f"<CarIdMapping(id={self.id}, real_car_id={self.real_car_id}, car_uuid={self.car_uuid})>"

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": str(self.uuid),
            "real_car_id": self.real_car_id,
            "car_uuid": str(self.car_uuid),
            "first_seen_at": self.first_seen_at.isoformat() if self.first_seen_at else None,
            "last_used_at": self.last_used_at.isoformat() if self.last_used_at else None,
            "usage_count": self.usage_count,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

    def update_usage(self):
        """更新使用统计"""
        from app.utils.timezone import to_utc_for_db
        self.last_used_at = to_utc_for_db()
        self.usage_count += 1

    def soft_delete(self):
        """软删除"""
        from app.utils.timezone import to_utc_for_db
        self.is_deleted = True
        self.deleted_at = to_utc_for_db()


class CarComparison(Base):
    """车辆对比模型"""

    __tablename__ = "car_comparisons"

    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    uuid = Column(UUID(as_uuid=True), unique=True, default=uuid.uuid4, index=True, nullable=False, comment="对比UUID")

    # 关联信息
    conversation_id = Column(UUID(as_uuid=True), ForeignKey('ai_conversations.uuid'), nullable=False, index=True, comment="对话ID")
    message_id = Column(String(100), nullable=False, index=True, comment="关联的消息ID")
    chat_message_uuid = Column(UUID(as_uuid=True), ForeignKey('chat_messages.uuid'), nullable=True, index=True, comment="关联的聊天消息UUID")

    # 对比信息
    car_id_1 = Column(String(50), nullable=False, comment="第一辆车的真实ID（不对外暴露）")
    car_id_2 = Column(String(50), nullable=False, comment="第二辆车的真实ID（不对外暴露）")
    car_uuid_1 = Column(UUID(as_uuid=True), nullable=False, index=True, comment="第一辆车的对外UUID")
    car_uuid_2 = Column(UUID(as_uuid=True), nullable=False, index=True, comment="第二辆车的对外UUID")

    task_type = Column(String(50), nullable=False, comment="任务类型")
    comparison_data = Column(JSON, comment="完整对比数据")
    slots_data = Column(JSON, comment="槽位数据")

    # 状态信息
    is_deleted = Column(Boolean, default=False, nullable=False, comment="是否删除")

    # 时间信息
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")
    deleted_at = Column(DateTime(timezone=True), comment="删除时间")

    # 关系
    chat_message = relationship("ChatMessage", backref="car_comparisons")

    def __repr__(self):
        return f"<CarComparison(id={self.id}, uuid={self.uuid}, car_id_1={self.car_id_1}, car_id_2={self.car_id_2})>"

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典（隐藏真实car_id）"""
        return {
            "id": str(self.uuid),  # 使用UUID作为对外ID
            "conversation_id": str(self.conversation_id),
            "message_id": self.message_id,
            "task_type": self.task_type,
            "car_1_id": str(self.car_uuid_1),  # 使用UUID作为对外ID
            "car_2_id": str(self.car_uuid_2),  # 使用UUID作为对外ID
            "comparison_data": self.comparison_data,
            "slots_data": self.slots_data,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

    def to_dict_with_car_ids(self) -> Dict[str, Any]:
        """转换为字典（包含真实car_id，仅内部使用）"""
        result = self.to_dict()
        result["real_car_id_1"] = self.car_id_1
        result["real_car_id_2"] = self.car_id_2
        return result

    def soft_delete(self):
        """软删除"""
        from app.utils.timezone import to_utc_for_db
        self.is_deleted = True
        self.deleted_at = to_utc_for_db()
