#!/usr/bin/env python3
"""
修复车辆对比表的唯一约束问题
删除 car_uuid_1 和 car_uuid_2 字段的唯一约束，允许同一辆车参与多次对比
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import create_engine, text, inspect
from sqlalchemy.orm import sessionmaker
from app.config import settings
import structlog

# 配置日志
logger = structlog.get_logger("car_comparison_fix")

# ANSI 颜色代码
GREEN = '\033[92m'
RED = '\033[91m'
YELLOW = '\033[93m'
BLUE = '\033[94m'
RESET = '\033[0m'

def print_success(message):
    print(f"{GREEN}✅ {message}{RESET}")

def print_error(message):
    print(f"{RED}❌ {message}{RESET}")

def print_warning(message):
    print(f"{YELLOW}⚠️ {message}{RESET}")

def print_info(message):
    print(f"{BLUE}ℹ️ {message}{RESET}")

# 创建数据库引擎
engine = create_engine(
    settings.database_url,
    pool_pre_ping=True,
    pool_recycle=300,
    connect_args={"options": "-csearch_path=public"}
)


def check_current_constraints():
    """检查当前的约束情况"""
    print_info("检查当前 car_comparisons 表的约束...")
    
    try:
        with engine.connect() as conn:
            # 查询所有约束
            result = conn.execute(text("""
                SELECT 
                    tc.constraint_name,
                    tc.constraint_type,
                    kcu.column_name
                FROM information_schema.table_constraints AS tc
                JOIN information_schema.key_column_usage AS kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                WHERE tc.table_name = 'car_comparisons'
                    AND tc.constraint_type = 'UNIQUE'
                    AND kcu.column_name IN ('car_uuid_1', 'car_uuid_2')
                ORDER BY tc.constraint_name, kcu.ordinal_position
            """))
            
            constraints = list(result)
            
            if constraints:
                print_warning(f"发现 {len(constraints)} 个需要删除的唯一约束:")
                for constraint in constraints:
                    print(f"  - {constraint[0]} ({constraint[2]})")
                return [c[0] for c in constraints]
            else:
                print_success("未发现需要删除的唯一约束")
                return []
                
    except Exception as e:
        print_error(f"检查约束失败: {e}")
        return None


def drop_unique_constraints(constraint_names):
    """删除唯一约束"""
    if not constraint_names:
        print_info("没有需要删除的约束")
        return True
        
    print_info(f"开始删除 {len(constraint_names)} 个唯一约束...")
    
    try:
        with engine.connect() as conn:
            for constraint_name in constraint_names:
                print_info(f"删除约束: {constraint_name}")
                
                drop_sql = f"""
                ALTER TABLE car_comparisons 
                DROP CONSTRAINT IF EXISTS {constraint_name}
                """
                
                conn.execute(text(drop_sql))
                print_success(f"成功删除约束: {constraint_name}")
            
            conn.commit()
            print_success("所有唯一约束删除完成")
            return True
            
    except Exception as e:
        print_error(f"删除约束失败: {e}")
        return False


def add_indexes():
    """添加索引以提高查询性能"""
    print_info("添加索引以提高查询性能...")
    
    try:
        with engine.connect() as conn:
            # 为 car_uuid_1 添加索引（如果不存在）
            index_sql_1 = """
            CREATE INDEX IF NOT EXISTS idx_car_comparisons_car_uuid_1 
            ON car_comparisons (car_uuid_1)
            """
            
            # 为 car_uuid_2 添加索引（如果不存在）
            index_sql_2 = """
            CREATE INDEX IF NOT EXISTS idx_car_comparisons_car_uuid_2 
            ON car_comparisons (car_uuid_2)
            """
            
            # 为组合查询添加复合索引
            composite_index_sql = """
            CREATE INDEX IF NOT EXISTS idx_car_comparisons_conversation_cars 
            ON car_comparisons (conversation_id, car_uuid_1, car_uuid_2)
            """
            
            conn.execute(text(index_sql_1))
            conn.execute(text(index_sql_2))
            conn.execute(text(composite_index_sql))
            conn.commit()
            
            print_success("索引添加完成")
            return True
            
    except Exception as e:
        print_error(f"添加索引失败: {e}")
        return False


def verify_fix():
    """验证修复结果"""
    print_info("验证修复结果...")
    
    try:
        with engine.connect() as conn:
            # 检查是否还有唯一约束
            result = conn.execute(text("""
                SELECT COUNT(*) FROM information_schema.table_constraints AS tc
                JOIN information_schema.key_column_usage AS kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                WHERE tc.table_name = 'car_comparisons'
                    AND tc.constraint_type = 'UNIQUE'
                    AND kcu.column_name IN ('car_uuid_1', 'car_uuid_2')
            """))
            
            constraint_count = result.scalar()
            
            if constraint_count == 0:
                print_success("验证通过：所有问题约束已删除")
                
                # 检查索引是否存在
                index_result = conn.execute(text("""
                    SELECT COUNT(*) FROM pg_indexes 
                    WHERE tablename = 'car_comparisons' 
                    AND indexname LIKE 'idx_car_comparisons_car_uuid%'
                """))
                
                index_count = index_result.scalar()
                print_success(f"找到 {index_count} 个相关索引")
                
                return True
            else:
                print_error(f"验证失败：仍有 {constraint_count} 个唯一约束存在")
                return False
                
    except Exception as e:
        print_error(f"验证失败: {e}")
        return False


def force_drop_constraints():
    """强制删除可能存在的唯一约束"""
    print_info("强制删除可能存在的唯一约束...")

    # 常见的约束名称模式
    possible_constraints = [
        'car_comparisons_car_uuid_1_key',
        'car_comparisons_car_uuid_2_key',
        'car_comparisons_car_uuid_1_unique',
        'car_comparisons_car_uuid_2_unique'
    ]

    try:
        with engine.connect() as conn:
            for constraint_name in possible_constraints:
                try:
                    print_info(f"尝试删除约束: {constraint_name}")

                    drop_sql = f"""
                    ALTER TABLE car_comparisons
                    DROP CONSTRAINT IF EXISTS {constraint_name}
                    """

                    conn.execute(text(drop_sql))
                    print_success(f"处理约束: {constraint_name}")
                except Exception as e:
                    print_warning(f"处理约束 {constraint_name} 时出错: {e}")

            conn.commit()
            print_success("强制删除约束完成")
            return True

    except Exception as e:
        print_error(f"强制删除约束失败: {e}")
        return False


def fix_car_comparison_constraints():
    """执行完整的修复流程"""
    print_info("🚀 开始修复车辆对比表的唯一约束问题...")

    # 测试数据库连接
    try:
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
            print_success("数据库连接成功")
    except Exception as e:
        print_error(f"数据库连接失败: {e}")
        return False

    # 步骤1: 检查当前约束
    constraint_names = check_current_constraints()
    if constraint_names is None:
        return False

    # 步骤2: 删除唯一约束（包括强制删除）
    if not drop_unique_constraints(constraint_names):
        return False

    # 步骤2.5: 强制删除可能遗漏的约束
    if not force_drop_constraints():
        print_warning("强制删除约束失败，但继续执行")

    # 步骤3: 添加索引
    if not add_indexes():
        print_warning("添加索引失败，但主要修复已完成")

    # 步骤4: 验证修复
    if not verify_fix():
        print_warning("验证失败，但修复可能已完成")

    print_success("🎉 车辆对比表唯一约束修复完成！")
    print_info("现在同一辆车可以参与多次对比了")
    return True


if __name__ == "__main__":
    success = fix_car_comparison_constraints()
    if not success:
        print_error("修复失败！")
        sys.exit(1)
    else:
        print_success("修复成功！")
